import 'package:flutter/material.dart';
import '../models/exercise.dart';
import '../utils/app_theme.dart';
import '../utils/constants.dart';

class ExerciseSelector extends StatefulWidget {
  final List<Exercise> exercises;
  final Function(Exercise exercise, String variation) onExerciseSelected;

  const ExerciseSelector({
    super.key,
    required this.exercises,
    required this.onExerciseSelected,
  });

  @override
  State<ExerciseSelector> createState() => _ExerciseSelectorState();
}

class _ExerciseSelectorState extends State<ExerciseSelector> {
  final _searchController = TextEditingController();
  String _selectedMuscleGroup = 'All';
  List<Exercise> _filteredExercises = [];

  @override
  void initState() {
    super.initState();
    _filteredExercises = widget.exercises;
  }

  void _filterExercises() {
    setState(() {
      _filteredExercises = widget.exercises.where((exercise) {
        final matchesSearch = _searchController.text.isEmpty ||
            exercise.name.toLowerCase().contains(_searchController.text.toLowerCase()) ||
            exercise.primaryMuscleGroup.toLowerCase().contains(_searchController.text.toLowerCase());
        
        final matchesMuscleGroup = _selectedMuscleGroup == 'All' ||
            exercise.primaryMuscleGroup == _selectedMuscleGroup ||
            exercise.secondaryMuscleGroups.contains(_selectedMuscleGroup);
        
        return matchesSearch && matchesMuscleGroup;
      }).toList();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.8,
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        children: [
          // Header
          Row(
            children: [
              const Text(
                'Select Exercise',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              IconButton(
                onPressed: () => Navigator.of(context).pop(),
                icon: const Icon(Icons.close),
              ),
            ],
          ),
          
          const SizedBox(height: AppConstants.defaultPadding),
          
          // Search Bar
          TextField(
            controller: _searchController,
            decoration: const InputDecoration(
              labelText: 'Search exercises',
              prefixIcon: Icon(Icons.search),
              border: OutlineInputBorder(),
            ),
            onChanged: (_) => _filterExercises(),
          ),
          
          const SizedBox(height: AppConstants.defaultPadding),
          
          // Muscle Group Filter
          SizedBox(
            height: 40,
            child: ListView(
              scrollDirection: Axis.horizontal,
              children: [
                _buildMuscleGroupChip('All'),
                ...AppConstants.muscleGroups.map((group) => _buildMuscleGroupChip(group)),
              ],
            ),
          ),
          
          const SizedBox(height: AppConstants.defaultPadding),
          
          // Exercise List
          Expanded(
            child: _filteredExercises.isEmpty
                ? const Center(
                    child: Text('No exercises found'),
                  )
                : ListView.builder(
                    itemCount: _filteredExercises.length,
                    itemBuilder: (context, index) {
                      final exercise = _filteredExercises[index];
                      return _buildExerciseCard(exercise);
                    },
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildMuscleGroupChip(String muscleGroup) {
    final isSelected = _selectedMuscleGroup == muscleGroup;
    
    return Padding(
      padding: const EdgeInsets.only(right: AppConstants.smallPadding),
      child: FilterChip(
        label: Text(muscleGroup),
        selected: isSelected,
        onSelected: (selected) {
          setState(() {
            _selectedMuscleGroup = muscleGroup;
          });
          _filterExercises();
        },
        selectedColor: AppTheme.primaryColor.withOpacity(0.2),
        checkmarkColor: AppTheme.primaryColor,
      ),
    );
  }

  Widget _buildExerciseCard(Exercise exercise) {
    return Card(
      child: ListTile(
        title: Text(exercise.name),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Primary: ${exercise.primaryMuscleGroup}',
              style: const TextStyle(fontSize: 12),
            ),
            if (exercise.secondaryMuscleGroups.isNotEmpty)
              Text(
                'Secondary: ${exercise.secondaryMuscleGroups.join(', ')}',
                style: const TextStyle(fontSize: 12),
              ),
            Text(
              'Equipment: ${exercise.equipmentRequired}',
              style: const TextStyle(fontSize: 12),
            ),
          ],
        ),
        trailing: Chip(
          label: Text(
            exercise.difficultyLevel,
            style: const TextStyle(fontSize: 10),
          ),
          backgroundColor: _getDifficultyColor(exercise.difficultyLevel),
        ),
        onTap: () => _showVariationSelector(exercise),
      ),
    );
  }

  Color _getDifficultyColor(String difficulty) {
    switch (difficulty.toLowerCase()) {
      case 'beginner':
        return AppTheme.successColor.withOpacity(0.2);
      case 'intermediate':
        return AppTheme.warningColor.withOpacity(0.2);
      case 'advanced':
        return AppTheme.errorColor.withOpacity(0.2);
      case 'expert':
        return Colors.purple.withOpacity(0.2);
      default:
        return Colors.grey.withOpacity(0.2);
    }
  }

  void _showVariationSelector(Exercise exercise) {
    // For now, we'll use the default variation
    // In a full implementation, you'd show a list of variations
    final variations = [
      exercise.defaultVariation,
      'Standard',
      'Wide Grip',
      'Close Grip',
      'Incline',
      'Decline',
    ];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Select Variation for ${exercise.name}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: variations.map((variation) {
            return ListTile(
              title: Text(variation),
              onTap: () {
                Navigator.of(context).pop();
                widget.onExerciseSelected(exercise, variation);
              },
            );
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}
