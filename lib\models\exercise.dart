class Exercise {
  final String id;
  final String name;
  final String description;
  final String primaryMuscleGroup;
  final List<String> secondaryMuscleGroups;
  final String equipmentRequired;
  final String difficultyLevel;
  final String instructions;
  final String defaultVariation;
  final bool isCustom;
  final DateTime createdAt;

  Exercise({
    required this.id,
    required this.name,
    required this.description,
    required this.primaryMuscleGroup,
    required this.secondaryMuscleGroups,
    required this.equipmentRequired,
    required this.difficultyLevel,
    required this.instructions,
    required this.defaultVariation,
    this.isCustom = false,
    required this.createdAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'primary_muscle_group': primaryMuscleGroup,
      'secondary_muscle_groups': secondaryMuscleGroups.join(','),
      'equipment_required': equipmentRequired,
      'difficulty_level': difficultyLevel,
      'instructions': instructions,
      'default_variation': defaultVariation,
      'is_custom': isCustom ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
    };
  }

  factory Exercise.fromMap(Map<String, dynamic> map) {
    return Exercise(
      id: map['id'],
      name: map['name'],
      description: map['description'],
      primaryMuscleGroup: map['primary_muscle_group'],
      secondaryMuscleGroups: map['secondary_muscle_groups']?.split(',') ?? [],
      equipmentRequired: map['equipment_required'],
      difficultyLevel: map['difficulty_level'],
      instructions: map['instructions'],
      defaultVariation: map['default_variation'],
      isCustom: map['is_custom'] == 1,
      createdAt: DateTime.parse(map['created_at']),
    );
  }

  Exercise copyWith({
    String? id,
    String? name,
    String? description,
    String? primaryMuscleGroup,
    List<String>? secondaryMuscleGroups,
    String? equipmentRequired,
    String? difficultyLevel,
    String? instructions,
    String? defaultVariation,
    bool? isCustom,
    DateTime? createdAt,
  }) {
    return Exercise(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      primaryMuscleGroup: primaryMuscleGroup ?? this.primaryMuscleGroup,
      secondaryMuscleGroups: secondaryMuscleGroups ?? this.secondaryMuscleGroups,
      equipmentRequired: equipmentRequired ?? this.equipmentRequired,
      difficultyLevel: difficultyLevel ?? this.difficultyLevel,
      instructions: instructions ?? this.instructions,
      defaultVariation: defaultVariation ?? this.defaultVariation,
      isCustom: isCustom ?? this.isCustom,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}
