# Astick Workout Tracker

A comprehensive Flutter application for tracking workouts, exercises, and fitness metrics with a focus on aesthetic physique development. Built specifically for personal use with a complete 5-day workout split program.

## Features

### ✅ Implemented Features
- **Dark Mode by Default** - Sleek dark theme for better user experience
- **Personal Profile with Gym Streak** - Track your consecutive workout days
- **Social Links Integration** - Editable social media links (Instagram, Twitter, YouTube, TikTok, LinkedIn, GitHub)
- **Comprehensive Exercise Database** - Complete 5-day workout split with 50+ exercises
- **Beautiful Exercise UI** - Color-coded muscle groups with icons and detailed information
- **Exercise Management** - Edit exercises, add YouTube videos and custom URLs
- **Custom Exercise Creation** - Add new exercises for specific muscle groups
- **Calendar-based Workout Tracking** - Monthly calendar view with workout indicators
- **Personal Metrics Dashboard** - Track weight, cardio sessions, and custom metrics
- **Material Design 3 UI** - Modern, intuitive interface with improved graphics
- **Theme Switching** - Toggle between light and dark modes
- **Workout Statistics** - Total workouts, monthly workouts, and gym streak tracking

### 🏋️ Complete Workout Program

#### DAY 1: LEGS
- **Quadriceps**: Barbell Back Squats, Leg Press, Walking Lunges, Leg Extensions
- **Hamstrings**: Romanian Deadlifts, Lying Leg Curls, Glute-Ham Raises
- **Calves**: Standing Calf Raises, Seated Calf Raises
- **Glutes**: Hip Thrusts, Bulgarian Split Squats

#### DAY 2: SHOULDERS
- **Front Deltoids**: Seated Dumbbell Shoulder Press, Front Raises, Landmine Press
- **Lateral Deltoids**: Lateral Raises, Cable Y-Raises, Upright Rows
- **Rear Deltoids**: Face Pulls, Reverse Pec Deck, Bent-Over Lateral Raises

#### DAY 3: BACK & BICEPS
- **Lats**: Pull-Ups/Lat Pulldowns, Barbell Rows, Single-Arm Dumbbell Rows, Straight-Arm Pulldowns
- **Mid-Back**: Seated Cable Rows, Chest-Supported Rows
- **Traps**: Barbell Shrugs, Dumbbell Shrugs
- **Biceps**: Incline Dumbbell Curls, Hammer Curls, Concentration Curls, Spider Curls
- **Brachialis**: Reverse Curls, Cross-Body Curls

#### DAY 4: REST
- Active recovery recommendations included

#### DAY 5: CHEST & TRICEPS
- **Upper Chest**: Incline Barbell Bench Press, Incline Dumbbell Press, Low-to-High Cable Flyes
- **Middle Chest**: Flat Barbell Bench Press, Flat Dumbbell Press, Cable Crossovers
- **Lower Chest**: Decline Bench Press, Dips, High-to-Low Cable Flyes
- **Inner Chest**: Cable Crossovers, Diamond Push-Ups, Plate Squeeze Press
- **Triceps**: Overhead Extensions, Skull Crushers, Tricep Pushdowns, Close-Grip Bench Press

### 🎨 Enhanced UI/UX Features
- **Color-coded Muscle Groups** - Each muscle group has its own color and icon
- **Exercise Cards with Gradients** - Beautiful visual design with muscle group indicators
- **Difficulty Badges** - Color-coded difficulty levels (Beginner, Intermediate, Advanced)
- **Expandable Exercise Details** - Comprehensive information including instructions and equipment
- **Social Links Grid** - Visual representation of social media profiles
- **Statistics Cards** - Eye-catching cards showing gym streak, total workouts, and monthly progress

## Architecture

### Tech Stack
- **Framework**: Flutter 3.x
- **State Management**: Provider
- **Database**: SQLite with encryption
- **Charts**: FL Chart
- **Calendar**: Table Calendar
- **Authentication**: Local Auth (biometric)

### Project Structure
```
lib/
├── models/           # Data models (User, Workout, Exercise, etc.)
├── providers/        # State management (WorkoutProvider, MetricsProvider)
├── screens/          # UI screens (Home, Workout Logging, etc.)
├── widgets/          # Reusable UI components
├── services/         # Database and business logic
└── utils/            # Constants, themes, and utilities
```

### Key Components
- **WorkoutProvider**: Manages workout state, exercise selection, and workout logging
- **MetricsProvider**: Handles personal metrics tracking and analytics
- **DatabaseService**: SQLite database operations with encryption
- **AppTheme**: Consistent theming following Material Design

## Getting Started

### Prerequisites
- Flutter SDK 3.0+
- Dart 3.0+
- Android Studio / VS Code
- Android SDK / Xcode (for mobile development)

### Installation
1. Clone the repository
```bash
git clone <repository-url>
cd my_workout
```

2. Install dependencies
```bash
flutter pub get
```

3. Run the app
```bash
flutter run
```

### Running Tests
```bash
flutter test
```

## Usage

### Creating a Workout
1. Open the app and navigate to the Home screen
2. Tap the floating action button (+) to start a new workout
3. Select exercises from the comprehensive database
4. Choose exercise variations
5. Log sets, reps, and weights
6. Add pre/post workout weight and cardio details
7. Save the workout

### Tracking Metrics
1. Navigate to the Metrics screen
2. View charts and trends for various metrics
3. Add new metric entries
4. Export data for external analysis

### Exercise Database
1. Navigate to the Exercises screen
2. Browse exercises by muscle group
3. Search for specific exercises
4. View detailed exercise instructions and variations

## Data Models

### Core Entities
- **UserProfile**: User information and preferences
- **Workout**: Workout sessions with metadata
- **Exercise**: Exercise definitions with muscle groups and instructions
- **WorkoutExercise**: Exercises performed in a specific workout
- **PersonalMetric**: Tracked personal metrics with privacy levels
- **CustomMetric**: User-defined metrics

### Privacy & Security
- Local data storage with SQLite encryption
- Granular privacy controls for sensitive metrics
- Optional biometric authentication
- No data sharing without explicit consent

## Contributing

### Development Guidelines
1. Follow Flutter best practices
2. Use Provider for state management
3. Implement proper error handling
4. Write tests for new features
5. Follow Material Design principles

### Code Style
- Use meaningful variable and function names
- Add comments for complex logic
- Follow Dart style guidelines
- Keep functions small and focused

## Known Issues
- setState during build warnings (non-critical, app functions correctly)
- Some UI overflow issues on smaller screens
- Database operations need optimization for large datasets

## Roadmap

### Phase 1 (Current)
- ✅ Basic UI and navigation
- ✅ Exercise database
- ✅ Workout logging
- 🚧 Data persistence

### Phase 2
- CSV export functionality
- Enhanced metrics dashboard
- Workout templates
- Progress tracking

### Phase 3
- Cloud sync and backup
- Advanced analytics
- Health app integration
- Social features

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support, feature requests, or bug reports, please create an issue in the repository.

---

**Note**: This is a personal fitness tracking application focused on privacy and data ownership. All data is stored locally by default, with optional cloud backup features.
