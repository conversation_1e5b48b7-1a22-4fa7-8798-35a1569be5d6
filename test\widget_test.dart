// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter_test/flutter_test.dart';

import 'package:my_workout/main.dart';

void main() {
  testWidgets('App loads successfully', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const MyWorkoutApp());

    // Verify that the app loads with the main navigation
    expect(find.text('My Workout Tracker'), findsOneWidget);

    // Verify bottom navigation is present
    expect(find.text('Home'), findsOneWidget);
    expect(find.text('Exercises'), findsOneWidget);
    expect(find.text('Metrics'), findsOneWidget);
    expect(find.text('Profile'), findsOneWidget);
  });
}
