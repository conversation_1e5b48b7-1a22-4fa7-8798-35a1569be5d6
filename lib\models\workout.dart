class Workout {
  final String id;
  final String userId;
  final DateTime date;
  final DateTime? startTime;
  final DateTime? endTime;
  final double? preWorkoutWeight;
  final double? postWorkoutWeight;
  final bool cardioPerformed;
  final String? cardioDetails;
  final String? notes;
  final int? overallRating;
  final DateTime createdAt;
  final DateTime updatedAt;

  Workout({
    required this.id,
    required this.userId,
    required this.date,
    this.startTime,
    this.endTime,
    this.preWorkoutWeight,
    this.postWorkoutWeight,
    this.cardioPerformed = false,
    this.cardioDetails,
    this.notes,
    this.overallRating,
    required this.createdAt,
    required this.updatedAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'user_id': userId,
      'date': date.toIso8601String(),
      'start_time': startTime?.toIso8601String(),
      'end_time': endTime?.toIso8601String(),
      'pre_workout_weight': preWorkoutWeight,
      'post_workout_weight': postWorkoutWeight,
      'cardio_performed': cardioPerformed ? 1 : 0,
      'cardio_details': cardioDetails,
      'notes': notes,
      'overall_rating': overallRating,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  factory Workout.fromMap(Map<String, dynamic> map) {
    return Workout(
      id: map['id'],
      userId: map['user_id'],
      date: DateTime.parse(map['date']),
      startTime: map['start_time'] != null ? DateTime.parse(map['start_time']) : null,
      endTime: map['end_time'] != null ? DateTime.parse(map['end_time']) : null,
      preWorkoutWeight: map['pre_workout_weight']?.toDouble(),
      postWorkoutWeight: map['post_workout_weight']?.toDouble(),
      cardioPerformed: map['cardio_performed'] == 1,
      cardioDetails: map['cardio_details'],
      notes: map['notes'],
      overallRating: map['overall_rating'],
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: DateTime.parse(map['updated_at']),
    );
  }

  Workout copyWith({
    String? id,
    String? userId,
    DateTime? date,
    DateTime? startTime,
    DateTime? endTime,
    double? preWorkoutWeight,
    double? postWorkoutWeight,
    bool? cardioPerformed,
    String? cardioDetails,
    String? notes,
    int? overallRating,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Workout(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      date: date ?? this.date,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      preWorkoutWeight: preWorkoutWeight ?? this.preWorkoutWeight,
      postWorkoutWeight: postWorkoutWeight ?? this.postWorkoutWeight,
      cardioPerformed: cardioPerformed ?? this.cardioPerformed,
      cardioDetails: cardioDetails ?? this.cardioDetails,
      notes: notes ?? this.notes,
      overallRating: overallRating ?? this.overallRating,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Duration? get duration {
    if (startTime != null && endTime != null) {
      return endTime!.difference(startTime!);
    }
    return null;
  }
}
