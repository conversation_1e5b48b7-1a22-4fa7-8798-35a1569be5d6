class AppConstants {
  // App Information
  static const String appName = 'Astick Workout Tracker';
  static const String appVersion = '1.0.0';

  // Database
  static const String databaseName = 'workout_tracker.db';
  static const int databaseVersion = 1;

  // Shared Preferences Keys
  static const String keyUserId = 'user_id';
  static const String keyThemeMode = 'theme_mode';
  static const String keyPrivacyMode = 'privacy_mode';
  static const String keyBiometricEnabled = 'biometric_enabled';
  static const String keyAutoBackup = 'auto_backup';
  static const String keyFirstLaunch = 'first_launch';

  // Muscle Groups
  static const List<String> muscleGroups = [
    'Chest',
    'Back',
    'Shoulders',
    'Biceps',
    'Triceps',
    'Legs',
    'Quadriceps',
    'Hamstrings',
    'Calves',
    'Glutes',
    'Core',
    'Forearms',
  ];

  // Equipment Types
  static const List<String> equipmentTypes = [
    'Barbell',
    'Dumbbell',
    'Cable Machine',
    'Resistance Bands',
    'Bodyweight',
    'Machine',
    'Kettlebell',
    'Medicine Ball',
    'Pull-up Bar',
    'Bench',
    'Squat Rack',
    'Smith Machine',
  ];

  // Difficulty Levels
  static const List<String> difficultyLevels = [
    'Beginner',
    'Intermediate',
    'Advanced',
    'Expert',
  ];

  // Cardio Types
  static const List<String> cardioTypes = [
    'Running',
    'Walking',
    'Cycling',
    'Swimming',
    'Rowing',
    'Elliptical',
    'Stair Climber',
    'Jump Rope',
    'HIIT',
    'Other',
  ];

  // Workout Templates
  static const List<String> workoutTemplates = [
    'Push (Chest, Shoulders, Triceps)',
    'Pull (Back, Biceps)',
    'Legs (Quadriceps, Hamstrings, Glutes, Calves)',
    'Upper Body',
    'Lower Body',
    'Full Body',
    'Cardio',
    'Custom',
  ];

  // Default Personal Metrics
  static const List<String> defaultPersonalMetrics = [
    'Weight',
    'Body Fat %',
    'Sleep Hours',
    'Water Intake',
    'Energy Level',
    'Mood',
    'Stress Level',
  ];

  // Privacy Levels
  static const List<String> privacyLevels = [
    'Public',
    'Private',
    'Sensitive',
  ];

  // Export Formats
  static const List<String> exportFormats = [
    'CSV',
    'JSON',
    'PDF',
  ];

  // Date Formats
  static const String dateFormat = 'yyyy-MM-dd';
  static const String timeFormat = 'HH:mm';
  static const String dateTimeFormat = 'yyyy-MM-dd HH:mm';
  static const String displayDateFormat = 'MMM dd, yyyy';
  static const String displayTimeFormat = 'h:mm a';

  // Validation
  static const int minPasswordLength = 8;
  static const int maxNameLength = 50;
  static const int maxNotesLength = 500;
  static const double minWeight = 0.1;
  static const double maxWeight = 1000.0;
  static const int minReps = 1;
  static const int maxReps = 999;
  static const int minSets = 1;
  static const int maxSets = 20;

  // UI Constants
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double borderRadius = 8.0;
  static const double cardElevation = 2.0;

  // Animation Durations
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 300);
  static const Duration longAnimation = Duration(milliseconds: 500);

  // Chart Colors
  static const List<String> chartColors = [
    '#3F51B5', // Primary
    '#FF4081', // Secondary
    '#4CAF50', // Success
    '#FFC107', // Warning
    '#F44336', // Error
    '#9C27B0', // Purple
    '#00BCD4', // Cyan
    '#FF9800', // Orange
    '#795548', // Brown
    '#607D8B', // Blue Grey
  ];
}

class WorkoutConstants {
  // Default rest times (in seconds)
  static const int defaultRestTime = 90;
  static const int shortRestTime = 60;
  static const int longRestTime = 180;

  // Rating scales
  static const int minRating = 1;
  static const int maxRating = 5;

  // Workout duration limits (in minutes)
  static const int minWorkoutDuration = 5;
  static const int maxWorkoutDuration = 300; // 5 hours
}

class MetricConstants {
  // Weight units
  static const String weightUnitKg = 'kg';
  static const String weightUnitLbs = 'lbs';

  // Distance units
  static const String distanceUnitKm = 'km';
  static const String distanceUnitMiles = 'miles';

  // Time units
  static const String timeUnitMinutes = 'minutes';
  static const String timeUnitHours = 'hours';

  // Default metric ranges
  static const double minBodyWeight = 30.0;
  static const double maxBodyWeight = 300.0;
  static const double minHeight = 100.0; // cm
  static const double maxHeight = 250.0; // cm
  static const int minAge = 13;
  static const int maxAge = 100;
}

class ErrorMessages {
  static const String networkError = 'Network connection error. Please check your internet connection.';
  static const String databaseError = 'Database error occurred. Please try again.';
  static const String validationError = 'Please check your input and try again.';
  static const String authenticationError = 'Authentication failed. Please try again.';
  static const String permissionError = 'Permission denied. Please grant necessary permissions.';
  static const String fileError = 'File operation failed. Please try again.';
  static const String exportError = 'Export failed. Please try again.';
  static const String importError = 'Import failed. Please check the file format.';
  static const String biometricError = 'Biometric authentication failed.';
  static const String genericError = 'An unexpected error occurred. Please try again.';
}

class SuccessMessages {
  static const String workoutSaved = 'Workout saved successfully!';
  static const String exerciseAdded = 'Exercise added successfully!';
  static const String metricRecorded = 'Metric recorded successfully!';
  static const String dataExported = 'Data exported successfully!';
  static const String dataImported = 'Data imported successfully!';
  static const String settingsSaved = 'Settings saved successfully!';
  static const String profileUpdated = 'Profile updated successfully!';
  static const String backupCreated = 'Backup created successfully!';
  static const String dataRestored = 'Data restored successfully!';
}
