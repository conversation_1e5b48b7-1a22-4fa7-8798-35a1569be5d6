class WorkoutExercise {
  final String id;
  final String workoutId;
  final String exerciseId;
  final String variationUsed;
  final List<ExerciseSet> sets;
  final String? notes;
  final int? rating;
  final int orderIndex;
  final DateTime createdAt;

  WorkoutExercise({
    required this.id,
    required this.workoutId,
    required this.exerciseId,
    required this.variationUsed,
    required this.sets,
    this.notes,
    this.rating,
    required this.orderIndex,
    required this.createdAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'workout_id': workoutId,
      'exercise_id': exerciseId,
      'variation_used': variationUsed,
      'sets_data': sets.map((set) => set.toMap()).toList().toString(),
      'notes': notes,
      'rating': rating,
      'order_index': orderIndex,
      'created_at': createdAt.toIso8601String(),
    };
  }

  factory WorkoutExercise.fromMap(Map<String, dynamic> map) {
    List<ExerciseSet> parsedSets = [];
    if (map['sets_data'] != null) {
      // Parse sets data from string representation
      // This is a simplified parsing - in production, use JSON encoding
      parsedSets = []; // TODO: Implement proper parsing
    }

    return WorkoutExercise(
      id: map['id'],
      workoutId: map['workout_id'],
      exerciseId: map['exercise_id'],
      variationUsed: map['variation_used'],
      sets: parsedSets,
      notes: map['notes'],
      rating: map['rating'],
      orderIndex: map['order_index'],
      createdAt: DateTime.parse(map['created_at']),
    );
  }

  WorkoutExercise copyWith({
    String? id,
    String? workoutId,
    String? exerciseId,
    String? variationUsed,
    List<ExerciseSet>? sets,
    String? notes,
    int? rating,
    int? orderIndex,
    DateTime? createdAt,
  }) {
    return WorkoutExercise(
      id: id ?? this.id,
      workoutId: workoutId ?? this.workoutId,
      exerciseId: exerciseId ?? this.exerciseId,
      variationUsed: variationUsed ?? this.variationUsed,
      sets: sets ?? this.sets,
      notes: notes ?? this.notes,
      rating: rating ?? this.rating,
      orderIndex: orderIndex ?? this.orderIndex,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}

class ExerciseSet {
  final int setNumber;
  final int reps;
  final double weight;
  final int? restTimeSeconds;
  final bool completed;

  ExerciseSet({
    required this.setNumber,
    required this.reps,
    required this.weight,
    this.restTimeSeconds,
    this.completed = false,
  });

  Map<String, dynamic> toMap() {
    return {
      'set_number': setNumber,
      'reps': reps,
      'weight': weight,
      'rest_time_seconds': restTimeSeconds,
      'completed': completed,
    };
  }

  factory ExerciseSet.fromMap(Map<String, dynamic> map) {
    return ExerciseSet(
      setNumber: map['set_number'],
      reps: map['reps'],
      weight: map['weight'].toDouble(),
      restTimeSeconds: map['rest_time_seconds'],
      completed: map['completed'] ?? false,
    );
  }

  ExerciseSet copyWith({
    int? setNumber,
    int? reps,
    double? weight,
    int? restTimeSeconds,
    bool? completed,
  }) {
    return ExerciseSet(
      setNumber: setNumber ?? this.setNumber,
      reps: reps ?? this.reps,
      weight: weight ?? this.weight,
      restTimeSeconds: restTimeSeconds ?? this.restTimeSeconds,
      completed: completed ?? this.completed,
    );
  }
}
