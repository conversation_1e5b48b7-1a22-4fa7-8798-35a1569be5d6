import 'package:flutter/foundation.dart';
import 'package:uuid/uuid.dart';
import '../models/personal_metric.dart';
import '../services/database_service.dart';

class MetricsProvider with ChangeNotifier {
  final DatabaseService _databaseService = DatabaseService();
  final Uuid _uuid = const Uuid();

  List<PersonalMetric> _metrics = [];
  List<CustomMetric> _customMetrics = [];
  bool _isLoading = false;
  String? _error;

  // Getters
  List<PersonalMetric> get metrics => _metrics;
  List<CustomMetric> get customMetrics => _customMetrics;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Load metrics for date range
  Future<void> loadMetrics(DateTime start, DateTime end, {String? metricType}) async {
    try {
      _setLoading(true);
      _metrics = await _databaseService.getPersonalMetricsByDateRange(
        start, 
        end, 
        metricType: metricType,
      );
      _clearError();
    } catch (e) {
      _setError('Failed to load metrics: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Add personal metric
  Future<bool> addPersonalMetric({
    required String userId,
    required DateTime date,
    required String metricType,
    required dynamic metricValue,
    String? notes,
    required PrivacyLevel privacyLevel,
  }) async {
    try {
      _setLoading(true);
      
      final metric = PersonalMetric(
        id: _uuid.v4(),
        userId: userId,
        date: date,
        metricType: metricType,
        metricValue: metricValue,
        notes: notes,
        privacyLevel: privacyLevel,
        createdAt: DateTime.now(),
      );

      await _databaseService.insertPersonalMetric(metric);
      _metrics.insert(0, metric);
      
      _clearError();
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Failed to add metric: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Get metrics by type
  List<PersonalMetric> getMetricsByType(String metricType) {
    return _metrics.where((metric) => metric.metricType == metricType).toList();
  }

  // Get latest metric value
  PersonalMetric? getLatestMetric(String metricType) {
    final typeMetrics = getMetricsByType(metricType);
    if (typeMetrics.isEmpty) return null;
    
    typeMetrics.sort((a, b) => b.date.compareTo(a.date));
    return typeMetrics.first;
  }

  // Get metrics for specific date
  List<PersonalMetric> getMetricsForDate(DateTime date) {
    final dateStr = date.toIso8601String().split('T')[0];
    return _metrics.where((metric) {
      final metricDateStr = metric.date.toIso8601String().split('T')[0];
      return metricDateStr == dateStr;
    }).toList();
  }

  // Get weight metrics for chart
  List<PersonalMetric> getWeightMetrics() {
    return getMetricsByType('Weight')
      ..sort((a, b) => a.date.compareTo(b.date));
  }

  // Calculate weight change
  double? getWeightChange(int days) {
    final weightMetrics = getWeightMetrics();
    if (weightMetrics.length < 2) return null;

    final now = DateTime.now();
    final startDate = now.subtract(Duration(days: days));
    
    final recentMetrics = weightMetrics.where((metric) =>
      metric.date.isAfter(startDate)
    ).toList();
    
    if (recentMetrics.length < 2) return null;
    
    final latest = recentMetrics.last.metricValue as double;
    final earliest = recentMetrics.first.metricValue as double;
    
    return latest - earliest;
  }

  // Get average metric value
  double? getAverageMetricValue(String metricType, int days) {
    final now = DateTime.now();
    final startDate = now.subtract(Duration(days: days));
    
    final typeMetrics = getMetricsByType(metricType).where((metric) =>
      metric.date.isAfter(startDate) && metric.metricValue is num
    ).toList();
    
    if (typeMetrics.isEmpty) return null;
    
    final sum = typeMetrics.fold<double>(0, (sum, metric) =>
      sum + (metric.metricValue as num).toDouble()
    );
    
    return sum / typeMetrics.length;
  }

  // Get metric trend (positive = increasing, negative = decreasing, 0 = stable)
  int getMetricTrend(String metricType, int days) {
    final now = DateTime.now();
    final midDate = now.subtract(Duration(days: days ~/ 2));
    final startDate = now.subtract(Duration(days: days));
    
    final firstHalf = getMetricsByType(metricType).where((metric) =>
      metric.date.isAfter(startDate) && 
      metric.date.isBefore(midDate) &&
      metric.metricValue is num
    ).toList();
    
    final secondHalf = getMetricsByType(metricType).where((metric) =>
      metric.date.isAfter(midDate) &&
      metric.metricValue is num
    ).toList();
    
    if (firstHalf.isEmpty || secondHalf.isEmpty) return 0;
    
    final firstAvg = firstHalf.fold<double>(0, (sum, metric) =>
      sum + (metric.metricValue as num).toDouble()
    ) / firstHalf.length;
    
    final secondAvg = secondHalf.fold<double>(0, (sum, metric) =>
      sum + (metric.metricValue as num).toDouble()
    ) / secondHalf.length;
    
    final difference = secondAvg - firstAvg;
    const threshold = 0.1; // Adjust based on metric type
    
    if (difference > threshold) return 1;
    if (difference < -threshold) return -1;
    return 0;
  }

  // Get workout frequency
  Map<String, int> getWorkoutFrequency(int days) {
    // This would need to be implemented with workout data
    // For now, returning empty map
    return {};
  }

  // Export metrics to CSV format
  String exportMetricsToCSV({
    DateTime? startDate,
    DateTime? endDate,
    List<String>? metricTypes,
    bool includeSensitive = false,
  }) {
    var metricsToExport = _metrics;
    
    // Filter by date range
    if (startDate != null) {
      metricsToExport = metricsToExport.where((metric) =>
        metric.date.isAfter(startDate) || metric.date.isAtSameMomentAs(startDate)
      ).toList();
    }
    
    if (endDate != null) {
      metricsToExport = metricsToExport.where((metric) =>
        metric.date.isBefore(endDate) || metric.date.isAtSameMomentAs(endDate)
      ).toList();
    }
    
    // Filter by metric types
    if (metricTypes != null && metricTypes.isNotEmpty) {
      metricsToExport = metricsToExport.where((metric) =>
        metricTypes.contains(metric.metricType)
      ).toList();
    }
    
    // Filter by privacy level
    if (!includeSensitive) {
      metricsToExport = metricsToExport.where((metric) =>
        metric.privacyLevel != PrivacyLevel.sensitive
      ).toList();
    }
    
    // Sort by date
    metricsToExport.sort((a, b) => a.date.compareTo(b.date));
    
    // Generate CSV
    final buffer = StringBuffer();
    buffer.writeln('Date,Metric Type,Value,Notes,Privacy Level');
    
    for (final metric in metricsToExport) {
      buffer.writeln([
        metric.date.toIso8601String().split('T')[0],
        metric.metricType,
        metric.metricValue.toString(),
        metric.notes?.replaceAll(',', ';') ?? '',
        metric.privacyLevel.toString().split('.').last,
      ].join(','));
    }
    
    return buffer.toString();
  }

  // Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }
}
