import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../utils/app_theme.dart';
import '../utils/constants.dart';

class SetInputWidget extends StatefulWidget {
  final int setNumber;
  final int initialReps;
  final double initialWeight;
  final Function(int reps, double weight) onChanged;
  final VoidCallback? onRemove;

  const SetInputWidget({
    super.key,
    required this.setNumber,
    required this.initialReps,
    required this.initialWeight,
    required this.onChanged,
    this.onRemove,
  });

  @override
  State<SetInputWidget> createState() => _SetInputWidgetState();
}

class _SetInputWidgetState extends State<SetInputWidget> {
  late TextEditingController _repsController;
  late TextEditingController _weightController;
  bool _isCompleted = false;

  @override
  void initState() {
    super.initState();
    _repsController = TextEditingController(
      text: widget.initialReps > 0 ? widget.initialReps.toString() : '',
    );
    _weightController = TextEditingController(
      text: widget.initialWeight > 0 ? widget.initialWeight.toString() : '',
    );
  }

  void _updateValues() {
    final reps = int.tryParse(_repsController.text) ?? 0;
    final weight = double.tryParse(_weightController.text) ?? 0.0;
    
    if (reps > 0 && weight >= 0) {
      widget.onChanged(reps, weight);
      setState(() {
        _isCompleted = true;
      });
    } else {
      setState(() {
        _isCompleted = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppConstants.smallPadding),
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        border: Border.all(
          color: _isCompleted ? AppTheme.successColor : Colors.grey.shade300,
          width: _isCompleted ? 2 : 1,
        ),
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        color: _isCompleted 
            ? AppTheme.successColor.withOpacity(0.05)
            : Colors.transparent,
      ),
      child: Row(
        children: [
          // Set Number
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: _isCompleted ? AppTheme.successColor : AppTheme.primaryColor,
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Text(
                widget.setNumber.toString(),
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          
          const SizedBox(width: AppConstants.defaultPadding),
          
          // Reps Input
          Expanded(
            flex: 2,
            child: TextFormField(
              controller: _repsController,
              decoration: const InputDecoration(
                labelText: 'Reps',
                border: OutlineInputBorder(),
                contentPadding: EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
              ),
              keyboardType: TextInputType.number,
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
                LengthLimitingTextInputFormatter(3),
              ],
              onChanged: (_) => _updateValues(),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Required';
                }
                final reps = int.tryParse(value);
                if (reps == null || reps < WorkoutConstants.minRating || reps > WorkoutConstants.maxRating * 200) {
                  return 'Invalid';
                }
                return null;
              },
            ),
          ),
          
          const SizedBox(width: AppConstants.smallPadding),
          
          // Weight Input
          Expanded(
            flex: 2,
            child: TextFormField(
              controller: _weightController,
              decoration: const InputDecoration(
                labelText: 'Weight (kg)',
                border: OutlineInputBorder(),
                contentPadding: EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
              ),
              keyboardType: const TextInputType.numberWithOptions(decimal: true),
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
              ],
              onChanged: (_) => _updateValues(),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Required';
                }
                final weight = double.tryParse(value);
                if (weight == null || weight < 0 || weight > MetricConstants.maxBodyWeight * 3) {
                  return 'Invalid';
                }
                return null;
              },
            ),
          ),
          
          const SizedBox(width: AppConstants.smallPadding),
          
          // Completion Checkbox
          Checkbox(
            value: _isCompleted,
            onChanged: (value) {
              setState(() {
                _isCompleted = value ?? false;
              });
              if (_isCompleted) {
                _updateValues();
              }
            },
            activeColor: AppTheme.successColor,
          ),
          
          // Remove Button
          if (widget.onRemove != null)
            IconButton(
              onPressed: widget.onRemove,
              icon: const Icon(Icons.delete_outline),
              color: AppTheme.errorColor,
              iconSize: 20,
            ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _repsController.dispose();
    _weightController.dispose();
    super.dispose();
  }
}
