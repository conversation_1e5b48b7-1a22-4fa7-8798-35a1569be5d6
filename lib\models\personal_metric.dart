enum MetricType {
  boolean,
  number,
  text,
}

enum PrivacyLevel {
  public,
  private,
  sensitive,
}

class PersonalMetric {
  final String id;
  final String userId;
  final DateTime date;
  final String metricType;
  final dynamic metricValue;
  final String? notes;
  final PrivacyLevel privacyLevel;
  final DateTime createdAt;

  PersonalMetric({
    required this.id,
    required this.userId,
    required this.date,
    required this.metricType,
    required this.metricValue,
    this.notes,
    required this.privacyLevel,
    required this.createdAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'user_id': userId,
      'date': date.toIso8601String(),
      'metric_type': metricType,
      'metric_value': metricValue.toString(),
      'notes': notes,
      'privacy_level': privacyLevel.index,
      'created_at': createdAt.toIso8601String(),
    };
  }

  factory PersonalMetric.fromMap(Map<String, dynamic> map) {
    return PersonalMetric(
      id: map['id'],
      userId: map['user_id'],
      date: DateTime.parse(map['date']),
      metricType: map['metric_type'],
      metricValue: map['metric_value'],
      notes: map['notes'],
      privacyLevel: PrivacyLevel.values[map['privacy_level']],
      createdAt: DateTime.parse(map['created_at']),
    );
  }

  PersonalMetric copyWith({
    String? id,
    String? userId,
    DateTime? date,
    String? metricType,
    dynamic metricValue,
    String? notes,
    PrivacyLevel? privacyLevel,
    DateTime? createdAt,
  }) {
    return PersonalMetric(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      date: date ?? this.date,
      metricType: metricType ?? this.metricType,
      metricValue: metricValue ?? this.metricValue,
      notes: notes ?? this.notes,
      privacyLevel: privacyLevel ?? this.privacyLevel,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}

class CustomMetric {
  final String id;
  final String userId;
  final String metricName;
  final MetricType metricType;
  final PrivacyLevel defaultPrivacyLevel;
  final bool displayOnDashboard;
  final DateTime createdAt;

  CustomMetric({
    required this.id,
    required this.userId,
    required this.metricName,
    required this.metricType,
    required this.defaultPrivacyLevel,
    required this.displayOnDashboard,
    required this.createdAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'user_id': userId,
      'metric_name': metricName,
      'metric_type': metricType.index,
      'default_privacy_level': defaultPrivacyLevel.index,
      'display_on_dashboard': displayOnDashboard ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
    };
  }

  factory CustomMetric.fromMap(Map<String, dynamic> map) {
    return CustomMetric(
      id: map['id'],
      userId: map['user_id'],
      metricName: map['metric_name'],
      metricType: MetricType.values[map['metric_type']],
      defaultPrivacyLevel: PrivacyLevel.values[map['default_privacy_level']],
      displayOnDashboard: map['display_on_dashboard'] == 1,
      createdAt: DateTime.parse(map['created_at']),
    );
  }

  CustomMetric copyWith({
    String? id,
    String? userId,
    String? metricName,
    MetricType? metricType,
    PrivacyLevel? defaultPrivacyLevel,
    bool? displayOnDashboard,
    DateTime? createdAt,
  }) {
    return CustomMetric(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      metricName: metricName ?? this.metricName,
      metricType: metricType ?? this.metricType,
      defaultPrivacyLevel: defaultPrivacyLevel ?? this.defaultPrivacyLevel,
      displayOnDashboard: displayOnDashboard ?? this.displayOnDashboard,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}
