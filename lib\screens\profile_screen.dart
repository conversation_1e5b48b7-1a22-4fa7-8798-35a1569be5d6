import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';
import '../providers/theme_provider.dart';
import '../providers/user_provider.dart';
import '../utils/app_theme.dart';
import '../utils/constants.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Profile'),
        elevation: 0,
        backgroundColor: Colors.transparent,
      ),
      body: Consumer2<UserProvider, ThemeProvider>(
        builder: (context, userProvider, themeProvider, child) {
          if (userProvider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          return SingleChildScrollView(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Column(
              children: [
                // Profile Header with Gradient
                Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        AppTheme.primaryColor,
                        AppTheme.primaryColor.withValues(alpha: 0.7),
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  padding: const EdgeInsets.all(AppConstants.largePadding),
                  child: Column(
                    children: [
                      // Profile Avatar with Border
                      Container(
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          border: Border.all(color: Colors.white, width: 4),
                        ),
                        child: CircleAvatar(
                          radius: 50,
                          backgroundColor: AppTheme.secondaryColor,
                          child: Text(
                            userProvider.userProfile?.name.substring(0, 1).toUpperCase() ?? 'A',
                            style: const TextStyle(
                              fontSize: 32,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(height: AppConstants.defaultPadding),
                      Text(
                        userProvider.userProfile?.name ?? 'Astick',
                        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        userProvider.userProfile?.email ?? '<EMAIL>',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.white70,
                        ),
                      ),
                      const SizedBox(height: AppConstants.defaultPadding),
                      ElevatedButton.icon(
                        onPressed: _editProfile,
                        icon: const Icon(Icons.edit),
                        label: const Text('Edit Profile'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.white,
                          foregroundColor: AppTheme.primaryColor,
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: AppConstants.defaultPadding),

                // Stats Cards with Gym Streak
                Row(
                  children: [
                    Expanded(
                      child: _buildStatCard(
                        'Gym Streak',
                        '${userProvider.gymStreak}',
                        Icons.local_fire_department,
                        AppTheme.errorColor,
                        '${userProvider.gymStreak} days',
                      ),
                    ),
                    Expanded(
                      child: _buildStatCard(
                        'Total Workouts',
                        '${userProvider.totalWorkouts}',
                        Icons.fitness_center,
                        AppTheme.primaryColor,
                        'All time',
                      ),
                    ),
                    Expanded(
                      child: _buildStatCard(
                        'This Month',
                        '${userProvider.monthlyWorkouts}',
                        Icons.calendar_month,
                        AppTheme.successColor,
                        'Current month',
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: AppConstants.defaultPadding),

                // Social Links Section
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(AppConstants.defaultPadding),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'Social Links',
                              style: Theme.of(context).textTheme.titleMedium,
                            ),
                            IconButton(
                              onPressed: _editSocialLinks,
                              icon: const Icon(Icons.edit),
                            ),
                          ],
                        ),
                        const SizedBox(height: AppConstants.smallPadding),
                        _buildSocialLinksGrid(userProvider.socialLinks),
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: AppConstants.defaultPadding),

                // Settings
                Card(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(AppConstants.defaultPadding),
                        child: Text(
                          'Settings',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                      ),

                      SwitchListTile(
                        title: const Text('Dark Mode'),
                        subtitle: const Text('Use dark theme'),
                        value: themeProvider.isDarkMode,
                        onChanged: (value) {
                          themeProvider.toggleTheme();
                        },
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color, String subtitle) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: AppConstants.smallPadding),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                color: color,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              title,
              style: Theme.of(context).textTheme.titleSmall,
              textAlign: TextAlign.center,
            ),
            Text(
              subtitle,
              style: Theme.of(context).textTheme.bodySmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSocialLinksGrid(Map<String, String> socialLinks) {
    final socialIcons = {
      'Instagram': Icons.camera_alt,
      'Twitter': Icons.alternate_email,
      'YouTube': Icons.play_circle_filled,
      'TikTok': Icons.music_note,
      'LinkedIn': Icons.business,
      'GitHub': Icons.code,
    };

    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: socialLinks.entries.map((entry) {
        final platform = entry.key;
        final url = entry.value;
        final icon = socialIcons[platform] ?? Icons.link;

        return InkWell(
          onTap: url.isNotEmpty ? () => _launchUrl(url) : null,
          child: Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: url.isNotEmpty ? AppTheme.primaryColor.withValues(alpha: 0.1) : Colors.grey.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: url.isNotEmpty ? AppTheme.primaryColor : Colors.grey,
                width: 1,
              ),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  icon,
                  color: url.isNotEmpty ? AppTheme.primaryColor : Colors.grey,
                  size: 24,
                ),
                const SizedBox(height: 4),
                Text(
                  platform,
                  style: TextStyle(
                    fontSize: 12,
                    color: url.isNotEmpty ? AppTheme.primaryColor : Colors.grey,
                  ),
                ),
              ],
            ),
          ),
        );
      }).toList(),
    );
  }

  void _editProfile() {
    // TODO: Navigate to edit profile screen
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Edit Profile'),
        content: const Text('Profile editing will be implemented in the next phase.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _editSocialLinks() {
    showDialog(
      context: context,
      builder: (context) => _SocialLinksEditDialog(),
    );
  }

  Future<void> _launchUrl(String url) async {
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      url = 'https://$url';
    }

    final uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Could not launch $url')),
        );
      }
    }
  }
}

class _SocialLinksEditDialog extends StatefulWidget {
  @override
  State<_SocialLinksEditDialog> createState() => _SocialLinksEditDialogState();
}

class _SocialLinksEditDialogState extends State<_SocialLinksEditDialog> {
  final Map<String, TextEditingController> _controllers = {};

  @override
  void initState() {
    super.initState();
    final userProvider = Provider.of<UserProvider>(context, listen: false);

    for (final platform in userProvider.socialLinks.keys) {
      _controllers[platform] = TextEditingController(
        text: userProvider.socialLinks[platform] ?? '',
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Edit Social Links'),
      content: SizedBox(
        width: double.maxFinite,
        child: ListView(
          shrinkWrap: true,
          children: _controllers.entries.map((entry) {
            final platform = entry.key;
            final controller = entry.value;

            return Padding(
              padding: const EdgeInsets.only(bottom: 16),
              child: TextFormField(
                controller: controller,
                decoration: InputDecoration(
                  labelText: platform,
                  hintText: 'Enter your $platform URL or username',
                  border: const OutlineInputBorder(),
                ),
              ),
            );
          }).toList(),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _saveSocialLinks,
          child: const Text('Save'),
        ),
      ],
    );
  }

  void _saveSocialLinks() {
    final userProvider = Provider.of<UserProvider>(context, listen: false);

    for (final entry in _controllers.entries) {
      final platform = entry.key;
      final url = entry.value.text.trim();
      userProvider.updateSocialLink(platform, url);
    }

    Navigator.of(context).pop();
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Social links updated successfully!'),
        backgroundColor: AppTheme.successColor,
      ),
    );
  }

  @override
  void dispose() {
    for (final controller in _controllers.values) {
      controller.dispose();
    }
    super.dispose();
  }
}
