import '../models/exercise.dart';

class ExerciseDataService {
  static List<Exercise> getAllExercises() {
    return [
      // DAY 1: LEGS - Quadriceps
      Exercise(
        id: 'quad_001',
        name: 'Barbell Back Squats',
        description: 'Primary compound movement targeting overall quad development',
        primaryMuscleGroup: 'Quadriceps',
        secondaryMuscleGroups: ['Glutes', 'Hamstrings'],
        equipmentRequired: '<PERSON>bell, Squat Rack',
        difficultyLevel: 'Intermediate',
        instructions: 'Stand with feet shoulder-width apart, bar on upper back. Lower into squat position, keeping chest up and knees tracking over toes. Drive through heels to return to starting position.',
        defaultVariation: 'Standard Back Squat',
        createdAt: DateTime.now(),
      ),
      Exercise(
        id: 'quad_002',
        name: 'Leg Press',
        description: 'Allows heavy quad loading with back support',
        primaryMuscleGroup: 'Quadriceps',
        secondaryMuscleGroups: ['Glutes'],
        equipmentRequired: 'Leg Press Machine',
        difficultyLevel: 'Beginner',
        instructions: 'Sit in leg press machine with feet shoulder-width apart on platform. Lower weight by bending knees to 90 degrees, then press back to starting position.',
        defaultVariation: 'Standard Leg Press',
        createdAt: DateTime.now(),
      ),
      Exercise(
        id: 'quad_003',
        name: 'Walking Lunges',
        description: 'Dynamic quad exercise with functional movement',
        primaryMuscleGroup: 'Quadriceps',
        secondaryMuscleGroups: ['Glutes', 'Hamstrings'],
        equipmentRequired: 'Dumbbells (optional)',
        difficultyLevel: 'Intermediate',
        instructions: 'Step forward into lunge position, lowering back knee toward ground. Push off front foot to step into next lunge. Alternate legs with each step.',
        defaultVariation: 'Bodyweight Walking Lunges',
        createdAt: DateTime.now(),
      ),
      Exercise(
        id: 'quad_004',
        name: 'Leg Extensions',
        description: 'Isolation exercise for quad definition',
        primaryMuscleGroup: 'Quadriceps',
        secondaryMuscleGroups: [],
        equipmentRequired: 'Leg Extension Machine',
        difficultyLevel: 'Beginner',
        instructions: 'Sit in leg extension machine with back against pad. Extend legs until straight, pause, then slowly lower back to starting position.',
        defaultVariation: 'Standard Leg Extension',
        createdAt: DateTime.now(),
      ),

      // DAY 1: LEGS - Hamstrings
      Exercise(
        id: 'ham_001',
        name: 'Romanian Deadlifts',
        description: 'Targets hamstring length and glutes',
        primaryMuscleGroup: 'Hamstrings',
        secondaryMuscleGroups: ['Glutes', 'Lower Back'],
        equipmentRequired: 'Barbell or Dumbbells',
        difficultyLevel: 'Intermediate',
        instructions: 'Hold barbell with overhand grip. Keep legs slightly bent, hinge at hips to lower weight while keeping back straight. Feel stretch in hamstrings, then return to standing.',
        defaultVariation: 'Barbell Romanian Deadlift',
        createdAt: DateTime.now(),
      ),
      Exercise(
        id: 'ham_002',
        name: 'Lying Leg Curls',
        description: 'Isolation movement for hamstring development',
        primaryMuscleGroup: 'Hamstrings',
        secondaryMuscleGroups: [],
        equipmentRequired: 'Leg Curl Machine',
        difficultyLevel: 'Beginner',
        instructions: 'Lie face down on leg curl machine. Curl heels toward glutes, squeezing hamstrings at top. Slowly lower back to starting position.',
        defaultVariation: 'Standard Lying Leg Curl',
        createdAt: DateTime.now(),
      ),
      Exercise(
        id: 'ham_003',
        name: 'Glute-Ham Raises',
        description: 'Comprehensive posterior chain exercise',
        primaryMuscleGroup: 'Hamstrings',
        secondaryMuscleGroups: ['Glutes', 'Lower Back'],
        equipmentRequired: 'GHD Machine',
        difficultyLevel: 'Advanced',
        instructions: 'Position yourself in GHD machine. Lower torso while keeping legs straight, then use hamstrings and glutes to return to starting position.',
        defaultVariation: 'Standard Glute-Ham Raise',
        createdAt: DateTime.now(),
      ),

      // DAY 1: LEGS - Calves
      Exercise(
        id: 'calf_001',
        name: 'Standing Calf Raises',
        description: 'Targets gastrocnemius (upper calf)',
        primaryMuscleGroup: 'Calves',
        secondaryMuscleGroups: [],
        equipmentRequired: 'Calf Raise Machine or Dumbbells',
        difficultyLevel: 'Beginner',
        instructions: 'Stand with balls of feet on platform, heels hanging off. Rise up on toes as high as possible, pause, then slowly lower below platform level.',
        defaultVariation: 'Machine Standing Calf Raise',
        createdAt: DateTime.now(),
      ),
      Exercise(
        id: 'calf_002',
        name: 'Seated Calf Raises',
        description: 'Emphasizes soleus (lower calf)',
        primaryMuscleGroup: 'Calves',
        secondaryMuscleGroups: [],
        equipmentRequired: 'Seated Calf Raise Machine',
        difficultyLevel: 'Beginner',
        instructions: 'Sit in calf raise machine with knees under pads. Rise up on toes, pause at top, then slowly lower to stretch position.',
        defaultVariation: 'Machine Seated Calf Raise',
        createdAt: DateTime.now(),
      ),

      // DAY 1: LEGS - Glutes
      Exercise(
        id: 'glute_001',
        name: 'Hip Thrusts',
        description: 'Primary glute isolation exercise',
        primaryMuscleGroup: 'Glutes',
        secondaryMuscleGroups: ['Hamstrings'],
        equipmentRequired: 'Barbell, Bench',
        difficultyLevel: 'Intermediate',
        instructions: 'Sit with upper back against bench, barbell over hips. Drive through heels to lift hips up, squeezing glutes at top. Lower with control.',
        defaultVariation: 'Barbell Hip Thrust',
        createdAt: DateTime.now(),
      ),
      Exercise(
        id: 'glute_002',
        name: 'Bulgarian Split Squats',
        description: 'Unilateral exercise for quad and glute development',
        primaryMuscleGroup: 'Glutes',
        secondaryMuscleGroups: ['Quadriceps', 'Hamstrings'],
        equipmentRequired: 'Bench, Dumbbells (optional)',
        difficultyLevel: 'Intermediate',
        instructions: 'Stand 2-3 feet in front of bench, place rear foot on bench. Lower into lunge position, keeping front knee over ankle. Push through front heel to return.',
        defaultVariation: 'Bodyweight Bulgarian Split Squat',
        createdAt: DateTime.now(),
      ),

      // DAY 2: SHOULDERS - Front Deltoids
      Exercise(
        id: 'front_delt_001',
        name: 'Seated Dumbbell Shoulder Press',
        description: 'Compound movement for overall shoulder development',
        primaryMuscleGroup: 'Front Deltoids',
        secondaryMuscleGroups: ['Lateral Deltoids', 'Triceps'],
        equipmentRequired: 'Dumbbells, Bench',
        difficultyLevel: 'Intermediate',
        instructions: 'Sit with back supported, dumbbells at shoulder level. Press weights overhead until arms are extended. Lower with control to starting position.',
        defaultVariation: 'Seated Dumbbell Press',
        createdAt: DateTime.now(),
      ),
      Exercise(
        id: 'front_delt_002',
        name: 'Front Raises',
        description: 'Isolation exercise for front deltoid definition',
        primaryMuscleGroup: 'Front Deltoids',
        secondaryMuscleGroups: [],
        equipmentRequired: 'Dumbbells or Barbell',
        difficultyLevel: 'Beginner',
        instructions: 'Hold weights at sides with slight bend in elbows. Raise weights forward to shoulder height, pause, then lower with control.',
        defaultVariation: 'Dumbbell Front Raise',
        createdAt: DateTime.now(),
      ),
      Exercise(
        id: 'front_delt_003',
        name: 'Landmine Press',
        description: 'Angled pressing for front delt emphasis',
        primaryMuscleGroup: 'Front Deltoids',
        secondaryMuscleGroups: ['Lateral Deltoids', 'Core'],
        equipmentRequired: 'Barbell, Landmine Attachment',
        difficultyLevel: 'Intermediate',
        instructions: 'Hold end of barbell at chest level. Press weight up and forward in arc motion. Lower with control to starting position.',
        defaultVariation: 'Single-Arm Landmine Press',
        createdAt: DateTime.now(),
      ),

      // DAY 2: SHOULDERS - Lateral Deltoids
      Exercise(
        id: 'lat_delt_001',
        name: 'Lateral Raises',
        description: 'Classic side delt isolation with dumbbells',
        primaryMuscleGroup: 'Lateral Deltoids',
        secondaryMuscleGroups: [],
        equipmentRequired: 'Dumbbells',
        difficultyLevel: 'Beginner',
        instructions: 'Hold dumbbells at sides with slight bend in elbows. Raise weights out to sides until parallel to floor. Lower with control.',
        defaultVariation: 'Standing Dumbbell Lateral Raise',
        createdAt: DateTime.now(),
      ),
      Exercise(
        id: 'lat_delt_002',
        name: 'Cable Y-Raises',
        description: 'Cross-body movement for maximum stretch',
        primaryMuscleGroup: 'Lateral Deltoids',
        secondaryMuscleGroups: [],
        equipmentRequired: 'Cable Machine',
        difficultyLevel: 'Intermediate',
        instructions: 'Set cables at low position. Cross cables and raise arms up and out in Y-shape. Focus on squeezing shoulder blades together.',
        defaultVariation: 'Cable Y-Raise',
        createdAt: DateTime.now(),
      ),
      Exercise(
        id: 'lat_delt_003',
        name: 'Upright Rows',
        description: 'Compound movement targeting side delts',
        primaryMuscleGroup: 'Lateral Deltoids',
        secondaryMuscleGroups: ['Traps', 'Rear Deltoids'],
        equipmentRequired: 'Barbell or Dumbbells',
        difficultyLevel: 'Intermediate',
        instructions: 'Hold weight with narrow grip. Pull weight up along body to chest level, leading with elbows. Lower with control.',
        defaultVariation: 'Barbell Upright Row',
        createdAt: DateTime.now(),
      ),

      // DAY 2: SHOULDERS - Rear Deltoids
      Exercise(
        id: 'rear_delt_001',
        name: 'Face Pulls',
        description: 'Compound rear delt and upper back exercise',
        primaryMuscleGroup: 'Rear Deltoids',
        secondaryMuscleGroups: ['Rhomboids', 'Mid Traps'],
        equipmentRequired: 'Cable Machine, Rope Attachment',
        difficultyLevel: 'Beginner',
        instructions: 'Set cable at face height with rope attachment. Pull rope to face, separating hands and squeezing shoulder blades together.',
        defaultVariation: 'Cable Face Pull',
        createdAt: DateTime.now(),
      ),
      Exercise(
        id: 'rear_delt_002',
        name: 'Reverse Pec Deck',
        description: 'Machine isolation for rear delts',
        primaryMuscleGroup: 'Rear Deltoids',
        secondaryMuscleGroups: [],
        equipmentRequired: 'Reverse Pec Deck Machine',
        difficultyLevel: 'Beginner',
        instructions: 'Sit facing machine with chest against pad. Grab handles and pull arms back, squeezing shoulder blades together.',
        defaultVariation: 'Machine Reverse Pec Deck',
        createdAt: DateTime.now(),
      ),
      Exercise(
        id: 'rear_delt_003',
        name: 'Bent-Over Lateral Raises',
        description: 'Free weight rear delt isolation',
        primaryMuscleGroup: 'Rear Deltoids',
        secondaryMuscleGroups: [],
        equipmentRequired: 'Dumbbells',
        difficultyLevel: 'Intermediate',
        instructions: 'Bend forward at hips with dumbbells hanging down. Raise weights out to sides, squeezing shoulder blades together.',
        defaultVariation: 'Bent-Over Dumbbell Lateral Raise',
        createdAt: DateTime.now(),
      ),

      // DAY 3: BACK & BICEPS - Lats
      Exercise(
        id: 'lat_001',
        name: 'Pull-Ups/Lat Pulldowns',
        description: 'Primary width builder for the back',
        primaryMuscleGroup: 'Lats',
        secondaryMuscleGroups: ['Biceps', 'Rhomboids'],
        equipmentRequired: 'Pull-up Bar or Lat Pulldown Machine',
        difficultyLevel: 'Intermediate',
        instructions: 'Hang from bar with wide grip or sit at lat pulldown. Pull body up or weight down until chin clears bar or bar touches chest.',
        defaultVariation: 'Wide-Grip Pull-up',
        createdAt: DateTime.now(),
      ),
      Exercise(
        id: 'lat_002',
        name: 'Barbell Rows',
        description: 'Compound movement for back thickness',
        primaryMuscleGroup: 'Lats',
        secondaryMuscleGroups: ['Rhomboids', 'Biceps', 'Rear Deltoids'],
        equipmentRequired: 'Barbell',
        difficultyLevel: 'Intermediate',
        instructions: 'Bend forward at hips holding barbell. Pull weight to lower chest, squeezing shoulder blades together. Lower with control.',
        defaultVariation: 'Bent-Over Barbell Row',
        createdAt: DateTime.now(),
      ),
      Exercise(
        id: 'lat_003',
        name: 'Single-Arm Dumbbell Rows',
        description: 'Unilateral back development',
        primaryMuscleGroup: 'Lats',
        secondaryMuscleGroups: ['Rhomboids', 'Biceps'],
        equipmentRequired: 'Dumbbell, Bench',
        difficultyLevel: 'Beginner',
        instructions: 'Place one knee and hand on bench, hold dumbbell in opposite hand. Pull weight to hip, squeezing lat. Lower with control.',
        defaultVariation: 'Single-Arm Dumbbell Row',
        createdAt: DateTime.now(),
      ),
      Exercise(
        id: 'lat_004',
        name: 'Straight-Arm Pulldowns',
        description: 'Isolation movement for lat activation',
        primaryMuscleGroup: 'Lats',
        secondaryMuscleGroups: [],
        equipmentRequired: 'Cable Machine, Straight Bar',
        difficultyLevel: 'Beginner',
        instructions: 'Stand at cable machine with straight bar. Keep arms straight and pull bar down to thighs, focusing on lat contraction.',
        defaultVariation: 'Cable Straight-Arm Pulldown',
        createdAt: DateTime.now(),
      ),

      // DAY 3: BACK & BICEPS - Mid-Back (Rhomboids)
      Exercise(
        id: 'rhomb_001',
        name: 'Seated Cable Rows',
        description: 'Targets the mid-back with constant tension',
        primaryMuscleGroup: 'Rhomboids',
        secondaryMuscleGroups: ['Lats', 'Biceps'],
        equipmentRequired: 'Cable Machine, V-Handle',
        difficultyLevel: 'Beginner',
        instructions: 'Sit at cable row machine with V-handle. Pull handle to lower chest, squeezing shoulder blades together. Return with control.',
        defaultVariation: 'Seated Cable Row',
        createdAt: DateTime.now(),
      ),
      Exercise(
        id: 'rhomb_002',
        name: 'Chest-Supported Rows',
        description: 'Eliminates lower back involvement',
        primaryMuscleGroup: 'Rhomboids',
        secondaryMuscleGroups: ['Lats', 'Biceps'],
        equipmentRequired: 'Chest-Supported Row Machine',
        difficultyLevel: 'Beginner',
        instructions: 'Sit with chest against pad, grab handles. Pull handles back, squeezing shoulder blades together. Return with control.',
        defaultVariation: 'Machine Chest-Supported Row',
        createdAt: DateTime.now(),
      ),

      // DAY 3: BACK & BICEPS - Traps
      Exercise(
        id: 'trap_001',
        name: 'Barbell Shrugs',
        description: 'Primary trap builder',
        primaryMuscleGroup: 'Traps',
        secondaryMuscleGroups: [],
        equipmentRequired: 'Barbell',
        difficultyLevel: 'Beginner',
        instructions: 'Hold barbell with overhand grip. Shrug shoulders up toward ears, pause, then lower with control.',
        defaultVariation: 'Barbell Shrug',
        createdAt: DateTime.now(),
      ),
      Exercise(
        id: 'trap_002',
        name: 'Dumbbell Shrugs',
        description: 'Allows greater range of motion',
        primaryMuscleGroup: 'Traps',
        secondaryMuscleGroups: [],
        equipmentRequired: 'Dumbbells',
        difficultyLevel: 'Beginner',
        instructions: 'Hold dumbbells at sides. Shrug shoulders up and slightly back, pause, then lower with control.',
        defaultVariation: 'Dumbbell Shrug',
        createdAt: DateTime.now(),
      ),
    ];
  }
}
