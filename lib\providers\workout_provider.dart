import 'package:flutter/foundation.dart';
import 'package:uuid/uuid.dart';
import '../models/workout.dart';
import '../models/workout_exercise.dart';
import '../models/exercise.dart';
import '../services/database_service.dart';

class WorkoutProvider with ChangeNotifier {
  final DatabaseService _databaseService = DatabaseService();
  final Uuid _uuid = const Uuid();

  List<Workout> _workouts = [];
  List<Exercise> _exercises = [];
  Workout? _currentWorkout;
  List<WorkoutExercise> _currentWorkoutExercises = [];
  bool _isLoading = false;
  String? _error;

  // Getters
  List<Workout> get workouts => _workouts;
  List<Exercise> get exercises => _exercises;
  Workout? get currentWorkout => _currentWorkout;
  List<WorkoutExercise> get currentWorkoutExercises => _currentWorkoutExercises;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Initialize provider
  Future<void> initialize() async {
    // Don't load exercises automatically to avoid setState during build
    // Exercises will be loaded when needed
  }

  // Load exercises from database
  Future<void> loadExercises() async {
    try {
      _setLoading(true);
      _exercises = await _databaseService.getAllExercises();
      _clearError();
    } catch (e) {
      _setError('Failed to load exercises: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Load workouts for a date range
  Future<void> loadWorkouts(DateTime start, DateTime end) async {
    try {
      _setLoading(true);
      _workouts = await _databaseService.getWorkoutsByDateRange(start, end);
      _clearError();
    } catch (e) {
      _setError('Failed to load workouts: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Get workouts for a specific date
  Future<List<Workout>> getWorkoutsForDate(DateTime date) async {
    try {
      return await _databaseService.getWorkoutsByDate(date);
    } catch (e) {
      _setError('Failed to load workouts for date: $e');
      return [];
    }
  }

  // Start a new workout
  void startNewWorkout({
    required String userId,
    required DateTime date,
    DateTime? startTime,
  }) {
    _currentWorkout = Workout(
      id: _uuid.v4(),
      userId: userId,
      date: date,
      startTime: startTime ?? DateTime.now(),
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
    _currentWorkoutExercises.clear();
    notifyListeners();
  }

  // Add exercise to current workout
  void addExerciseToWorkout(Exercise exercise, String variation) {
    if (_currentWorkout == null) return;

    final workoutExercise = WorkoutExercise(
      id: _uuid.v4(),
      workoutId: _currentWorkout!.id,
      exerciseId: exercise.id,
      variationUsed: variation,
      sets: [],
      orderIndex: _currentWorkoutExercises.length,
      createdAt: DateTime.now(),
    );

    _currentWorkoutExercises.add(workoutExercise);
    notifyListeners();
  }

  // Add set to exercise
  void addSetToExercise(String workoutExerciseId, ExerciseSet set) {
    final index = _currentWorkoutExercises.indexWhere(
      (we) => we.id == workoutExerciseId,
    );

    if (index != -1) {
      final updatedSets = List<ExerciseSet>.from(_currentWorkoutExercises[index].sets);
      updatedSets.add(set);

      _currentWorkoutExercises[index] = _currentWorkoutExercises[index].copyWith(
        sets: updatedSets,
      );
      notifyListeners();
    }
  }

  // Update set in exercise
  void updateSetInExercise(String workoutExerciseId, int setIndex, ExerciseSet updatedSet) {
    final exerciseIndex = _currentWorkoutExercises.indexWhere(
      (we) => we.id == workoutExerciseId,
    );

    if (exerciseIndex != -1 && setIndex < _currentWorkoutExercises[exerciseIndex].sets.length) {
      final updatedSets = List<ExerciseSet>.from(_currentWorkoutExercises[exerciseIndex].sets);
      updatedSets[setIndex] = updatedSet;

      _currentWorkoutExercises[exerciseIndex] = _currentWorkoutExercises[exerciseIndex].copyWith(
        sets: updatedSets,
      );
      notifyListeners();
    }
  }

  // Remove set from exercise
  void removeSetFromExercise(String workoutExerciseId, int setIndex) {
    final exerciseIndex = _currentWorkoutExercises.indexWhere(
      (we) => we.id == workoutExerciseId,
    );

    if (exerciseIndex != -1 && setIndex < _currentWorkoutExercises[exerciseIndex].sets.length) {
      final updatedSets = List<ExerciseSet>.from(_currentWorkoutExercises[exerciseIndex].sets);
      updatedSets.removeAt(setIndex);

      // Update set numbers
      for (int i = 0; i < updatedSets.length; i++) {
        updatedSets[i] = updatedSets[i].copyWith(setNumber: i + 1);
      }

      _currentWorkoutExercises[exerciseIndex] = _currentWorkoutExercises[exerciseIndex].copyWith(
        sets: updatedSets,
      );
      notifyListeners();
    }
  }

  // Update current workout details
  void updateCurrentWorkout({
    DateTime? endTime,
    double? preWorkoutWeight,
    double? postWorkoutWeight,
    bool? cardioPerformed,
    String? cardioDetails,
    String? notes,
    int? overallRating,
  }) {
    if (_currentWorkout == null) return;

    _currentWorkout = _currentWorkout!.copyWith(
      endTime: endTime,
      preWorkoutWeight: preWorkoutWeight,
      postWorkoutWeight: postWorkoutWeight,
      cardioPerformed: cardioPerformed,
      cardioDetails: cardioDetails,
      notes: notes,
      overallRating: overallRating,
      updatedAt: DateTime.now(),
    );
    notifyListeners();
  }

  // Save current workout
  Future<bool> saveCurrentWorkout() async {
    if (_currentWorkout == null) return false;

    try {
      _setLoading(true);

      // Save workout
      await _databaseService.insertWorkout(_currentWorkout!);

      // Save workout exercises
      for (final workoutExercise in _currentWorkoutExercises) {
        // TODO: Save workout exercises to database
        // This would require implementing the workout_exercises table operations
      }

      // Add to local list
      _workouts.insert(0, _currentWorkout!);

      // Clear current workout
      _currentWorkout = null;
      _currentWorkoutExercises.clear();

      _clearError();
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Failed to save workout: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Cancel current workout
  void cancelCurrentWorkout() {
    _currentWorkout = null;
    _currentWorkoutExercises.clear();
    notifyListeners();
  }

  // Get exercises by muscle group
  List<Exercise> getExercisesByMuscleGroup(String muscleGroup) {
    return _exercises.where((exercise) =>
      exercise.primaryMuscleGroup == muscleGroup ||
      exercise.secondaryMuscleGroups.contains(muscleGroup)
    ).toList();
  }

  // Search exercises
  List<Exercise> searchExercises(String query) {
    if (query.isEmpty) return _exercises;

    final lowercaseQuery = query.toLowerCase();
    return _exercises.where((exercise) =>
      exercise.name.toLowerCase().contains(lowercaseQuery) ||
      exercise.primaryMuscleGroup.toLowerCase().contains(lowercaseQuery) ||
      exercise.secondaryMuscleGroups.any((group) =>
        group.toLowerCase().contains(lowercaseQuery)
      )
    ).toList();
  }

  // Add custom exercise
  Future<bool> addCustomExercise(Exercise exercise) async {
    try {
      _setLoading(true);
      await _databaseService.insertExercise(exercise);
      _exercises.add(exercise);
      _clearError();
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Failed to add exercise: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }
}
