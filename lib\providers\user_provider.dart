import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user_profile.dart';
import '../services/database_service.dart';

class UserProvider with ChangeNotifier {
  final DatabaseService _databaseService = DatabaseService();
  
  UserProfile? _userProfile;
  Map<String, String> _socialLinks = {};
  int _gymStreak = 0;
  int _totalWorkouts = 0;
  int _monthlyWorkouts = 0;
  bool _isLoading = false;
  String? _error;

  // Getters
  UserProfile? get userProfile => _userProfile;
  Map<String, String> get socialLinks => _socialLinks;
  int get gymStreak => _gymStreak;
  int get totalWorkouts => _totalWorkouts;
  int get monthlyWorkouts => _monthlyWorkouts;
  bool get isLoading => _isLoading;
  String? get error => _error;

  UserProvider() {
    _initializeUser();
  }

  Future<void> _initializeUser() async {
    await _loadUserProfile();
    await _loadSocialLinks();
    await _calculateStats();
  }

  Future<void> _loadUserProfile() async {
    try {
      _setLoading(true);
      
      // For now, create a default user profile
      // In a real app, this would load from database or authentication
      _userProfile = UserProfile(
        id: 'user_001',
        name: 'Astick',
        email: '<EMAIL>',
        privacySettings: {},
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
      
      _clearError();
    } catch (e) {
      _setError('Failed to load user profile: $e');
    } finally {
      _setLoading(false);
    }
  }

  Future<void> _loadSocialLinks() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Load social links from SharedPreferences
      _socialLinks = {
        'Instagram': prefs.getString('social_instagram') ?? '',
        'Twitter': prefs.getString('social_twitter') ?? '',
        'YouTube': prefs.getString('social_youtube') ?? '',
        'TikTok': prefs.getString('social_tiktok') ?? '',
        'LinkedIn': prefs.getString('social_linkedin') ?? '',
        'GitHub': prefs.getString('social_github') ?? '',
      };
      
      notifyListeners();
    } catch (e) {
      _setError('Failed to load social links: $e');
    }
  }

  Future<void> updateSocialLink(String platform, String url) async {
    try {
      _socialLinks[platform] = url;
      notifyListeners();
      
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('social_${platform.toLowerCase()}', url);
    } catch (e) {
      _setError('Failed to update social link: $e');
    }
  }

  Future<void> _calculateStats() async {
    try {
      // Calculate gym streak (consecutive days with workouts)
      _gymStreak = await _calculateGymStreak();
      
      // Calculate total workouts
      _totalWorkouts = await _calculateTotalWorkouts();
      
      // Calculate monthly workouts
      _monthlyWorkouts = await _calculateMonthlyWorkouts();
      
      notifyListeners();
    } catch (e) {
      _setError('Failed to calculate stats: $e');
    }
  }

  Future<int> _calculateGymStreak() async {
    try {
      final now = DateTime.now();
      int streak = 0;
      
      // Check each day going backwards from today
      for (int i = 0; i < 365; i++) { // Max 365 days
        final checkDate = now.subtract(Duration(days: i));
        final workouts = await _databaseService.getWorkoutsByDate(checkDate);
        
        if (workouts.isNotEmpty) {
          streak++;
        } else {
          // If today has no workout, don't break the streak yet
          if (i == 0) {
            continue;
          }
          break;
        }
      }
      
      return streak;
    } catch (e) {
      return 0;
    }
  }

  Future<int> _calculateTotalWorkouts() async {
    try {
      final startDate = DateTime(2020, 1, 1); // Far back date
      final endDate = DateTime.now();
      final workouts = await _databaseService.getWorkoutsByDateRange(startDate, endDate);
      return workouts.length;
    } catch (e) {
      return 0;
    }
  }

  Future<int> _calculateMonthlyWorkouts() async {
    try {
      final now = DateTime.now();
      final startOfMonth = DateTime(now.year, now.month, 1);
      final endOfMonth = DateTime(now.year, now.month + 1, 0);
      final workouts = await _databaseService.getWorkoutsByDateRange(startOfMonth, endOfMonth);
      return workouts.length;
    } catch (e) {
      return 0;
    }
  }

  Future<void> updateUserProfile({
    String? name,
    String? email,
    double? height,
    double? targetWeight,
    String? fitnessGoals,
  }) async {
    if (_userProfile == null) return;

    try {
      _setLoading(true);
      
      _userProfile = _userProfile!.copyWith(
        name: name,
        email: email,
        height: height,
        targetWeight: targetWeight,
        fitnessGoals: fitnessGoals,
        updatedAt: DateTime.now(),
      );
      
      // Save to database
      await _databaseService.updateUserProfile(_userProfile!);
      
      _clearError();
      notifyListeners();
    } catch (e) {
      _setError('Failed to update user profile: $e');
    } finally {
      _setLoading(false);
    }
  }

  Future<void> refreshStats() async {
    await _calculateStats();
  }

  // Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }
}
