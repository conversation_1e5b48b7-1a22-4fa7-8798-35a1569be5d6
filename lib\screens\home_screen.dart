import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:intl/intl.dart';
import '../providers/workout_provider.dart';
import '../providers/metrics_provider.dart';
import '../models/workout.dart';
import '../utils/app_theme.dart';
import '../utils/constants.dart';
import 'workout_logging_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  DateTime _focusedDay = DateTime.now();
  DateTime _selectedDay = DateTime.now();
  CalendarFormat _calendarFormat = CalendarFormat.month;
  Map<DateTime, List<Workout>> _workoutEvents = {};

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadWorkouts();
    });
  }

  Future<void> _loadWorkouts() async {
    final workoutProvider = Provider.of<WorkoutProvider>(context, listen: false);
    final startOfMonth = DateTime(_focusedDay.year, _focusedDay.month, 1);
    final endOfMonth = DateTime(_focusedDay.year, _focusedDay.month + 1, 0);

    await workoutProvider.loadWorkouts(startOfMonth, endOfMonth);

    // Group workouts by date
    _workoutEvents.clear();
    for (final workout in workoutProvider.workouts) {
      final date = DateTime(workout.date.year, workout.date.month, workout.date.day);
      if (_workoutEvents[date] != null) {
        _workoutEvents[date]!.add(workout);
      } else {
        _workoutEvents[date] = [workout];
      }
    }

    if (mounted) setState(() {});
  }

  List<Workout> _getEventsForDay(DateTime day) {
    return _workoutEvents[day] ?? [];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Astick'),
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              // Navigate to settings
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Calendar Widget
          Card(
            margin: const EdgeInsets.all(AppConstants.defaultPadding),
            child: TableCalendar<Workout>(
              firstDay: DateTime.utc(2020, 1, 1),
              lastDay: DateTime.utc(2030, 12, 31),
              focusedDay: _focusedDay,
              selectedDayPredicate: (day) => isSameDay(_selectedDay, day),
              calendarFormat: _calendarFormat,
              eventLoader: _getEventsForDay,
              startingDayOfWeek: StartingDayOfWeek.monday,
              calendarStyle: CalendarStyle(
                outsideDaysVisible: false,
                weekendTextStyle: TextStyle(color: AppTheme.secondaryColor),
                holidayTextStyle: TextStyle(color: AppTheme.secondaryColor),
                markerDecoration: BoxDecoration(
                  color: AppTheme.primaryColor,
                  shape: BoxShape.circle,
                ),
                selectedDecoration: BoxDecoration(
                  color: AppTheme.primaryColor,
                  shape: BoxShape.circle,
                ),
                todayDecoration: BoxDecoration(
                  color: AppTheme.secondaryColor,
                  shape: BoxShape.circle,
                ),
              ),
              headerStyle: const HeaderStyle(
                formatButtonVisible: true,
                titleCentered: true,
                formatButtonShowsNext: false,
                formatButtonDecoration: BoxDecoration(
                  color: AppTheme.primaryColor,
                  borderRadius: BorderRadius.all(Radius.circular(12.0)),
                ),
                formatButtonTextStyle: TextStyle(
                  color: Colors.white,
                ),
              ),
              onDaySelected: (selectedDay, focusedDay) {
                if (!isSameDay(_selectedDay, selectedDay)) {
                  setState(() {
                    _selectedDay = selectedDay;
                    _focusedDay = focusedDay;
                  });
                }
              },
              onFormatChanged: (format) {
                if (_calendarFormat != format) {
                  setState(() {
                    _calendarFormat = format;
                  });
                }
              },
              onPageChanged: (focusedDay) {
                _focusedDay = focusedDay;
                _loadWorkouts();
              },
            ),
          ),

          // Selected Day Workouts
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppConstants.defaultPadding,
                  ),
                  child: Text(
                    'Workouts for ${DateFormat('MMM dd, yyyy').format(_selectedDay)}',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                ),
                const SizedBox(height: AppConstants.smallPadding),
                Expanded(
                  child: _buildWorkoutsList(),
                ),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _startNewWorkout(),
        tooltip: 'Start New Workout',
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildWorkoutsList() {
    final workouts = _getEventsForDay(_selectedDay);

    if (workouts.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.fitness_center,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Text(
              'No workouts for this day',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: AppConstants.smallPadding),
            ElevatedButton(
              onPressed: () => _startNewWorkout(),
              child: const Text('Start Workout'),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: AppConstants.defaultPadding),
      itemCount: workouts.length,
      itemBuilder: (context, index) {
        final workout = workouts[index];
        return _buildWorkoutCard(workout);
      },
    );
  }

  Widget _buildWorkoutCard(Workout workout) {
    return Card(
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: AppTheme.primaryColor,
          child: const Icon(
            Icons.fitness_center,
            color: Colors.white,
          ),
        ),
        title: Text(
          workout.startTime != null
              ? 'Workout at ${DateFormat('h:mm a').format(workout.startTime!)}'
              : 'Workout',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (workout.duration != null)
              Text('Duration: ${_formatDuration(workout.duration!)}'),
            if (workout.notes != null && workout.notes!.isNotEmpty)
              Text(
                workout.notes!,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
          ],
        ),
        trailing: workout.overallRating != null
            ? Row(
                mainAxisSize: MainAxisSize.min,
                children: List.generate(
                  5,
                  (index) => Icon(
                    index < workout.overallRating!
                        ? Icons.star
                        : Icons.star_border,
                    color: AppTheme.warningColor,
                    size: 16,
                  ),
                ),
              )
            : null,
        onTap: () => _viewWorkoutDetails(workout),
      ),
    );
  }

  String _formatDuration(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);

    if (hours > 0) {
      return '${hours}h ${minutes}m';
    } else {
      return '${minutes}m';
    }
  }

  void _startNewWorkout() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => WorkoutLoggingScreen(
          selectedDate: _selectedDay,
        ),
      ),
    ).then((_) => _loadWorkouts());
  }

  void _viewWorkoutDetails(Workout workout) {
    // Navigate to workout details screen
    // TODO: Implement workout details screen
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Workout Details'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Date: ${DateFormat('MMM dd, yyyy').format(workout.date)}'),
            if (workout.startTime != null)
              Text('Start: ${DateFormat('h:mm a').format(workout.startTime!)}'),
            if (workout.endTime != null)
              Text('End: ${DateFormat('h:mm a').format(workout.endTime!)}'),
            if (workout.duration != null)
              Text('Duration: ${_formatDuration(workout.duration!)}'),
            if (workout.preWorkoutWeight != null)
              Text('Pre-workout weight: ${workout.preWorkoutWeight} kg'),
            if (workout.postWorkoutWeight != null)
              Text('Post-workout weight: ${workout.postWorkoutWeight} kg'),
            if (workout.cardioPerformed)
              Text('Cardio: ${workout.cardioDetails ?? 'Yes'}'),
            if (workout.notes != null && workout.notes!.isNotEmpty)
              Text('Notes: ${workout.notes}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}
