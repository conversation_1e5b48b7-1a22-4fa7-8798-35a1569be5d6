class UserProfile {
  final String id;
  final String name;
  final String email;
  final String? profilePicture;
  final double? height;
  final double? targetWeight;
  final String? fitnessGoals;
  final Map<String, dynamic> privacySettings;
  final DateTime createdAt;
  final DateTime updatedAt;

  UserProfile({
    required this.id,
    required this.name,
    required this.email,
    this.profilePicture,
    this.height,
    this.targetWeight,
    this.fitnessGoals,
    required this.privacySettings,
    required this.createdAt,
    required this.updatedAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'profile_picture': profilePicture,
      'height': height,
      'target_weight': targetWeight,
      'fitness_goals': fitnessGoals,
      'privacy_settings': privacySettings.toString(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  factory UserProfile.fromMap(Map<String, dynamic> map) {
    return UserProfile(
      id: map['id'],
      name: map['name'],
      email: map['email'],
      profilePicture: map['profile_picture'],
      height: map['height']?.toDouble(),
      targetWeight: map['target_weight']?.toDouble(),
      fitnessGoals: map['fitness_goals'],
      privacySettings: map['privacy_settings'] != null 
          ? Map<String, dynamic>.from(map['privacy_settings'])
          : {},
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: DateTime.parse(map['updated_at']),
    );
  }

  UserProfile copyWith({
    String? id,
    String? name,
    String? email,
    String? profilePicture,
    double? height,
    double? targetWeight,
    String? fitnessGoals,
    Map<String, dynamic>? privacySettings,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UserProfile(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      profilePicture: profilePicture ?? this.profilePicture,
      height: height ?? this.height,
      targetWeight: targetWeight ?? this.targetWeight,
      fitnessGoals: fitnessGoals ?? this.fitnessGoals,
      privacySettings: privacySettings ?? this.privacySettings,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
