import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../providers/workout_provider.dart';
import '../models/exercise.dart';
import '../models/workout_exercise.dart';
import '../utils/app_theme.dart';
import '../utils/constants.dart';
import '../widgets/exercise_selector.dart';
import '../widgets/set_input_widget.dart';

class WorkoutLoggingScreen extends StatefulWidget {
  final DateTime selectedDate;

  const WorkoutLoggingScreen({
    super.key,
    required this.selectedDate,
  });

  @override
  State<WorkoutLoggingScreen> createState() => _WorkoutLoggingScreenState();
}

class _WorkoutLoggingScreenState extends State<WorkoutLoggingScreen> {
  final _formKey = GlobalKey<FormState>();
  final _notesController = TextEditingController();
  final _preWeightController = TextEditingController();
  final _postWeightController = TextEditingController();
  final _cardioDetailsController = TextEditingController();

  bool _cardioPerformed = false;
  int _overallRating = 0;
  bool _isWorkoutStarted = false;

  @override
  void initState() {
    super.initState();
    _startWorkout();
  }

  void _startWorkout() {
    final workoutProvider = Provider.of<WorkoutProvider>(context, listen: false);
    workoutProvider.startNewWorkout(
      userId: 'user_001', // TODO: Get from auth provider
      date: widget.selectedDate,
      startTime: DateTime.now(),
    );
    setState(() {
      _isWorkoutStarted = true;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Workout - ${DateFormat('MMM dd').format(widget.selectedDate)}'),
        actions: [
          TextButton(
            onPressed: _saveWorkout,
            child: const Text(
              'Save',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
      body: Consumer<WorkoutProvider>(
        builder: (context, workoutProvider, child) {
          if (!_isWorkoutStarted || workoutProvider.currentWorkout == null) {
            return const Center(child: CircularProgressIndicator());
          }

          return Form(
            key: _formKey,
            child: Column(
              children: [
                // Workout Header
                _buildWorkoutHeader(workoutProvider),
                
                // Exercise List
                Expanded(
                  child: _buildExerciseList(workoutProvider),
                ),
                
                // Add Exercise Button
                Padding(
                  padding: const EdgeInsets.all(AppConstants.defaultPadding),
                  child: SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: () => _showExerciseSelector(workoutProvider),
                      icon: const Icon(Icons.add),
                      label: const Text('Add Exercise'),
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildWorkoutHeader(WorkoutProvider workoutProvider) {
    return Card(
      margin: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          children: [
            // Pre/Post Workout Weight
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _preWeightController,
                    decoration: const InputDecoration(
                      labelText: 'Pre-workout Weight (kg)',
                      prefixIcon: Icon(Icons.monitor_weight),
                    ),
                    keyboardType: TextInputType.number,
                    onChanged: (value) {
                      final weight = double.tryParse(value);
                      workoutProvider.updateCurrentWorkout(
                        preWorkoutWeight: weight,
                      );
                    },
                  ),
                ),
                const SizedBox(width: AppConstants.smallPadding),
                Expanded(
                  child: TextFormField(
                    controller: _postWeightController,
                    decoration: const InputDecoration(
                      labelText: 'Post-workout Weight (kg)',
                      prefixIcon: Icon(Icons.monitor_weight_outlined),
                    ),
                    keyboardType: TextInputType.number,
                    onChanged: (value) {
                      final weight = double.tryParse(value);
                      workoutProvider.updateCurrentWorkout(
                        postWorkoutWeight: weight,
                      );
                    },
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: AppConstants.defaultPadding),
            
            // Cardio Section
            Row(
              children: [
                Checkbox(
                  value: _cardioPerformed,
                  onChanged: (value) {
                    setState(() {
                      _cardioPerformed = value ?? false;
                    });
                    workoutProvider.updateCurrentWorkout(
                      cardioPerformed: _cardioPerformed,
                    );
                  },
                ),
                const Text('Cardio performed'),
              ],
            ),
            
            if (_cardioPerformed)
              TextFormField(
                controller: _cardioDetailsController,
                decoration: const InputDecoration(
                  labelText: 'Cardio details',
                  hintText: 'e.g., 20 min treadmill, 5km run',
                ),
                onChanged: (value) {
                  workoutProvider.updateCurrentWorkout(
                    cardioDetails: value.isEmpty ? null : value,
                  );
                },
              ),
            
            const SizedBox(height: AppConstants.defaultPadding),
            
            // Overall Rating
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text('Overall Rating:'),
                Row(
                  children: List.generate(5, (index) {
                    return IconButton(
                      onPressed: () {
                        setState(() {
                          _overallRating = index + 1;
                        });
                        workoutProvider.updateCurrentWorkout(
                          overallRating: _overallRating,
                        );
                      },
                      icon: Icon(
                        index < _overallRating ? Icons.star : Icons.star_border,
                        color: AppTheme.warningColor,
                      ),
                    );
                  }),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildExerciseList(WorkoutProvider workoutProvider) {
    final exercises = workoutProvider.currentWorkoutExercises;
    
    if (exercises.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.fitness_center,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Text(
              'No exercises added yet',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: AppConstants.smallPadding),
            const Text(
              'Tap "Add Exercise" to get started',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: AppConstants.defaultPadding),
      itemCount: exercises.length,
      itemBuilder: (context, index) {
        final workoutExercise = exercises[index];
        final exercise = workoutProvider.exercises.firstWhere(
          (e) => e.id == workoutExercise.exerciseId,
          orElse: () => Exercise(
            id: '',
            name: 'Unknown Exercise',
            description: '',
            primaryMuscleGroup: '',
            secondaryMuscleGroups: [],
            equipmentRequired: '',
            difficultyLevel: '',
            instructions: '',
            defaultVariation: '',
            createdAt: DateTime.now(),
          ),
        );
        
        return _buildExerciseCard(workoutProvider, workoutExercise, exercise);
      },
    );
  }

  Widget _buildExerciseCard(
    WorkoutProvider workoutProvider,
    WorkoutExercise workoutExercise,
    Exercise exercise,
  ) {
    return Card(
      child: ExpansionTile(
        title: Text(exercise.name),
        subtitle: Text(
          '${workoutExercise.variationUsed} • ${workoutExercise.sets.length} sets',
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Column(
              children: [
                // Sets List
                ...workoutExercise.sets.asMap().entries.map((entry) {
                  final index = entry.key;
                  final set = entry.value;
                  
                  return SetInputWidget(
                    setNumber: set.setNumber,
                    initialReps: set.reps,
                    initialWeight: set.weight,
                    onChanged: (reps, weight) {
                      final updatedSet = set.copyWith(
                        reps: reps,
                        weight: weight,
                        completed: true,
                      );
                      workoutProvider.updateSetInExercise(
                        workoutExercise.id,
                        index,
                        updatedSet,
                      );
                    },
                    onRemove: () {
                      workoutProvider.removeSetFromExercise(
                        workoutExercise.id,
                        index,
                      );
                    },
                  );
                }).toList(),
                
                // Add Set Button
                const SizedBox(height: AppConstants.smallPadding),
                SizedBox(
                  width: double.infinity,
                  child: OutlinedButton.icon(
                    onPressed: () {
                      final newSet = ExerciseSet(
                        setNumber: workoutExercise.sets.length + 1,
                        reps: 10,
                        weight: 0.0,
                      );
                      workoutProvider.addSetToExercise(
                        workoutExercise.id,
                        newSet,
                      );
                    },
                    icon: const Icon(Icons.add),
                    label: const Text('Add Set'),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showExerciseSelector(WorkoutProvider workoutProvider) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => ExerciseSelector(
        exercises: workoutProvider.exercises,
        onExerciseSelected: (exercise, variation) {
          workoutProvider.addExerciseToWorkout(exercise, variation);
          Navigator.of(context).pop();
        },
      ),
    );
  }

  Future<void> _saveWorkout() async {
    final workoutProvider = Provider.of<WorkoutProvider>(context, listen: false);
    
    // Update workout with final details
    workoutProvider.updateCurrentWorkout(
      endTime: DateTime.now(),
      notes: _notesController.text.isEmpty ? null : _notesController.text,
    );
    
    // Save workout
    final success = await workoutProvider.saveCurrentWorkout();
    
    if (success) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(SuccessMessages.workoutSaved),
            backgroundColor: AppTheme.successColor,
          ),
        );
        Navigator.of(context).pop();
      }
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(workoutProvider.error ?? ErrorMessages.genericError),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    }
  }

  @override
  void dispose() {
    _notesController.dispose();
    _preWeightController.dispose();
    _postWeightController.dispose();
    _cardioDetailsController.dispose();
    super.dispose();
  }
}
